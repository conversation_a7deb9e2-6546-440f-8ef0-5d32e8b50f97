<?php
$currency = $this->customlib->getSchoolCurrencyFormat();
?>

<div class="pb30">
    <table class="table table-striped mb0 font13">
        <tbody>
            <tr>
                <th class="bozero"><?php echo $this->lang->line('name'); ?></th>
                <td class="bozero"><?php echo $this->customlib->getFullName($student['firstname'], $student['middlename'], $student['lastname'], $sch_setting->middlename, $sch_setting->lastname); ?></td>
                <th class="bozero"><?php echo $this->lang->line('class_section'); ?></th>
                <td class="bozero"><?php echo $student['class'] . " (" . $student['section'] . ")" ?> </td>
            </tr>
            <tr>
                <th><?php echo $this->lang->line('father_name'); ?></th>
                <td><?php echo $student['father_name']; ?></td>
                <th><?php echo $this->lang->line('admission_no'); ?></th>
                <td><?php echo $student['admission_no']; ?></td>
            </tr>
            <tr>
                <th><?php echo $this->lang->line('mobile_number'); ?></th>
                <td><?php echo $student['mobileno']; ?></td>
                <th><?php echo $this->lang->line('roll_number'); ?></th>
                <td> <?php echo $student['roll_no']; ?>
            </td>
        </tr>
        <tr>
            <th><?php echo $this->lang->line("pickup"); ?></th>
            <td>
                <?php if(!empty($route_pickup_point->name)){ echo $route_pickup_point->name; } ?>
            </td>
            <th><?php echo $this->lang->line("pickup_time"); ?></th>
            <td> <?php if(!empty($route_pickup_point->pickup_time)){ echo $route_pickup_point->pickup_time; } ?>
        </td>
    </tr>
    <tr>
        <th><?php echo $this->lang->line("fees"); ?> (<?php echo $currency; ?>)</th>
        <td><?php if(!empty($route_pickup_point->fees)){ echo amountFormat($route_pickup_point->fees); } ?> </td>
        <th><?php echo $this->lang->line("distance_km"); ?></th>
        <td> <?php if(!empty($route_pickup_point->destination_distance)){ echo $route_pickup_point->destination_distance; } ?>
    </td>
</tr>

</tbody>
</table>
<hr class="hrexamfirstrow">

<?php
if (!empty($fees)) { 
    ?>
    <input type="hidden" name="student_session_id" value="<?php echo $student_session_id; ?>">
    <input type="hidden" name="class_id" value="<?php echo $student['class_id']; ?>">
    <input type="hidden" name="section_id" value="<?php echo $student['section_id']; ?>">
    <input type="hidden" name="student_id" value="<?php echo $student['id']; ?>">
    <div class="scroll-area">
        <?php
            if ($this->module_lib->hasActive('transport')) {
                ?>
                <div class="bozero">
                    <h4 class="pagetitleh2">
                        <?php echo $this->lang->line('transport_details'); ?>
                    </h4>

                    <div class="row around10">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('route_list'); ?></label>
                                <select  class="form-control" id="vehroute_id" name="vehroute_id">

                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                    <?php
                                    foreach ($vehroutelist as $vehroute) {
                                        ?>
                                        <optgroup data-route-id ="<?php echo $vehroute['id']; ?>" label=" <?php echo $vehroute['route_title']; ?>">
                                            <?php
                                            $vehicles = $vehroute['vehicles'];
                                            if (!empty($vehicles)) {
                                                foreach ($vehicles as $key => $value) {
                                                    ?>

                                                    <option  value="<?php echo $value->vec_route_id ?>">
                                                        <?php echo $value->vehicle_no ?>
                                                    </option>
                                                    <?php
                                                }
                                            }
                                            ?>
                                        </optgroup>
                                        <?php
                                    }
                                    ?>
                                </select>
                                <input type="hidden" name="route_id" id="hidden_route_id" value="">
                                <span class="text-danger"><?php echo form_error('vehroute_id'); ?></span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label for="exampleInputEmail1"><?php echo $this->lang->line('pickup_point'); ?></label>
                                <select  class="form-control" id="pickup_point" name="route_pickup_point_id">
                                    <option value=""><?php echo $this->lang->line('select'); ?></option>
                                </select>

                                <span class="text-danger"><?php echo form_error('route_pickup_point_id'); ?></span>
                            </div>
                        </div>

                        </div>
                    </div>
                </div>
            <?php }?>
            <table class="table table-striped table-bordered table-list">
              <thead>
                <tr>
                    <th>
                        <div class="chk">
                            <input type="checkbox"  class="chkall">
                            <label >  <?php echo $this->lang->line("month"); ?></label>
                        </div>
                    </th>
                    <th><?php echo $this->lang->line("due_date"); ?></th>
                    <th class="text text-center"><?php echo $this->lang->line("fine_type"); ?></th>
                    <th class="text text-right"><?php echo $this->lang->line("amount"); ?></th>
                </tr>
            </thead>
            <tbody>
              <?php

              if(!empty($route_pickup_point)) :
              foreach ($fees as $fee_key => $fee_value) {
                ?>
                    <input type="hidden" name="student_transport_fee_id_<?php echo $fee_value['id']; ?>" value="<?php echo $fee_value['student_transport_fee_id']; ?>">
                    <?php if(!empty($fee_value['student_transport_fee_id'])) { ?>
                    <input type="hidden" name="prev_ids[]" value="<?php echo $fee_value['student_transport_fee_id']; ?>">
                    <?php } ?>
                    <tr>
                        <td>
                            <div class="chk">
                                <input type="checkbox"  name="transport_route_fee[]" value="<?php echo $fee_value['id'] ?>" <?php  if($fee_value['student_transport_fee_id'] != 0 ) echo "checked"; ?> class="check_month">
                                <label ><?php echo $this->lang->line(strtolower($fee_value['month'])) ?></label>
                            </div>
                        </td>
                        <td><?php echo $this->customlib->dateformat($fee_value['due_date']); ?></td>
                        <td class="text text-center"><?php echo $this->lang->line($fee_value['fine_type']) ?></td>
                        <td class="text text-right"><?php
                            if ($fee_value['fine_type'] == "fix") {
                                echo amountFormat($fee_value['fine_amount']) . ' ' . $currency;
                            } elseif ($fee_value['fine_type'] == "percentage") {
                                echo $fee_value['fine_percentage'] . "%";
                            } ?> 
                        </td>
                    </tr>
            <?php } endif; ?>
</tbody>
</table>
</div>
</div>
<div class="sticky-footer">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <?php if (($this->rbac->hasPrivilege('student_transport_fees', 'can_add')) || ($this->rbac->hasPrivilege('student_transport_fees', 'can_edit'))) {?>
            <button type="submit" class="btn btn-primary pull-right" data-loading-text="<?php echo $this->lang->line('submitting') ?>" value=""><?php echo $this->lang->line('save'); ?></button>
        <?php }?>
    </div>
</div>
<?php
} else {
    ?>
    <div class="alert alert-info">
      <?php echo "(You have not created Transport fees master please add before assign --r)" ?>
  </div>
  <?php
}
?>
<script>
$(document).ready(function() {
        // Get the initially selected route ID
        var vehroute_id = $('#vehroute_id').val(); 
        // If a route is already selected (for existing student transport assignment)
        if (vehroute_id != "") {
            // Get the pickup point ID from the route_pickup_point object
            var pickup_point_id = <?php echo (!empty($route_pickup_point->route_pickup_point_id)) ? $route_pickup_point->route_pickup_point_id : 0; ?>;
            
            // Set the hidden route ID
            var route_id = $('#vehroute_id option:selected').closest('optgroup').data('route-id');
            $('#hidden_route_id').val(route_id);
            
            // Call the function with the correct pickup point ID
            get_pickup_point(vehroute_id, pickup_point_id);
        }
    });
</script> 

<script type="text/javascript">
    
    $(document).ready(function() {
        // Get values from student variable
        var vehroute_id = "<?php echo (!empty($student['vehroute_id'])) ? $student['vehroute_id'] : ''; ?>";
        var pickup_point_id = "<?php echo (!empty($student['route_pickup_point_id'])) ? $student['route_pickup_point_id'] : 0; ?>";
        var route_id = "<?php echo (!empty($student['route_id'])) ? $student['route_id'] : ''; ?>";
        
        // If student has a vehroute_id assigned
        if (vehroute_id != "") {
            // Preselect the route dropdown
            $('#vehroute_id').val(vehroute_id);
            
            // Set the hidden route ID
            $('#hidden_route_id').val(route_id);
            
            // Call the function to populate and select the pickup point
            get_pickup_point(vehroute_id, pickup_point_id);
        }
    });
     
</script>